# Database
DATABASE_URL="postgresql://username:password@localhost:5432/wiki_spark_collaborate?schema=public"

# JWT
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
JWT_EXPIRES_IN="7d"

# Server
PORT=3001
NODE_ENV="development"

# CORS
FRONTEND_URL="http://localhost:5173"

# Email (for notifications and password reset)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"
FROM_EMAIL="<EMAIL>"

# File Upload
MAX_FILE_SIZE=5242880
UPLOAD_PATH="./uploads"

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Wikimedia API (for integration)
WIKIMEDIA_API_URL="https://en.wikipedia.org/api/rest_v1"
WIKIDATA_API_URL="https://www.wikidata.org/w/api.php"

# Admin
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="change-this-secure-password"

# Logging
LOG_LEVEL="info"
LOG_FILE="./logs/app.log"
