import { Router } from 'express';
import { authenticate, authorize } from '../middleware/auth.js';
import { validate, schemas } from '../middleware/validation.js';
import { asyncHandler } from '../middleware/errorHandler.js';

const router = Router();

/**
 * @swagger
 * /api/admin/users:
 *   get:
 *     summary: Get all users (Admin only)
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Users retrieved successfully
 */
router.get('/users',
  authenticate,
  authorize('ADMIN', 'SUPER_ADMIN'),
  validate({ query: schemas.paginationQuery }),
  asyncHandler(async (req, res) => {
    res.json({
      success: true,
      message: 'Admin routes - Coming soon!',
      data: { endpoint: 'GET /api/admin/users' }
    });
  })
);

/**
 * @swagger
 * /api/admin/analytics:
 *   get:
 *     summary: Get platform analytics
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Analytics retrieved successfully
 */
router.get('/analytics',
  authenticate,
  authorize('ADMIN', 'SUPER_ADMIN'),
  asyncHandler(async (req, res) => {
    res.json({
      success: true,
      message: 'Platform analytics - Coming soon!',
      data: { endpoint: 'GET /api/admin/analytics' }
    });
  })
);

export default router;
