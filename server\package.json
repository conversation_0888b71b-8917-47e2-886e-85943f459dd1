{"name": "wiki-spark-collaborate-server", "version": "1.0.0", "description": "Backend API for Wiki Spark Collaborate - Wikimedia Education Platform", "main": "dist/index.js", "type": "module", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "tsx src/scripts/seed.ts", "test": "jest", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "keywords": ["wikimedia", "wikipedia", "education", "learning", "africa", "api", "backend"], "author": "Wiki Spark Collaborate Team", "license": "MIT", "dependencies": {"@prisma/client": "^5.22.0", "express": "^4.21.1", "cors": "^2.8.5", "helmet": "^8.0.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "zod": "^3.23.8", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.15", "rate-limiter-flexible": "^5.0.3", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "dotenv": "^16.4.5", "winston": "^3.15.0"}, "devDependencies": {"@types/express": "^5.0.0", "@types/cors": "^2.8.17", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.7", "@types/multer": "^1.4.12", "@types/nodemailer": "^6.4.15", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.6", "@types/node": "^22.5.5", "@typescript-eslint/eslint-plugin": "^8.0.1", "@typescript-eslint/parser": "^8.0.1", "eslint": "^9.9.0", "jest": "^29.7.0", "@types/jest": "^29.5.13", "ts-jest": "^29.2.5", "tsx": "^4.19.1", "typescript": "^5.5.3", "prisma": "^5.22.0"}}