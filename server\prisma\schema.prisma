// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                String   @id @default(cuid())
  email             String   @unique
  username          String   @unique
  firstName         String
  lastName          String
  password          String
  avatar            String?
  bio               String?
  country           String?
  timezone          String?
  preferredLanguage String   @default("en")
  role              UserRole @default(LEARNER)
  isEmailVerified   Boolean  @default(false)
  emailVerifyToken  String?
  passwordResetToken String?
  passwordResetExpires DateTime?
  lastLoginAt       DateTime?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Learning Progress
  enrollments       Enrollment[]
  lessonProgress    LessonProgress[]
  achievements      UserAchievement[]
  certificates      Certificate[]
  quizAttempts      QuizAttempt[]

  // Community
  forumPosts        ForumPost[]
  forumComments     ForumComment[]
  mentorships       Mentorship[] @relation("MentorUser")
  menteeships       Mentorship[] @relation("MenteeUser")

  // Content Creation (for instructors)
  createdCourses    Course[] @relation("CourseInstructor")
  createdLessons    Lesson[] @relation("LessonCreator")

  @@map("users")
}

model Course {
  id          String      @id @default(cuid())
  title       String
  slug        String      @unique
  description String
  thumbnail   String?
  level       CourseLevel
  category    String
  tags        String[]
  isPublished Boolean     @default(false)
  price       Float       @default(0)
  duration    Int? // in minutes
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relationships
  instructorId String
  instructor   User   @relation("CourseInstructor", fields: [instructorId], references: [id])
  
  modules      Module[]
  enrollments  Enrollment[]
  reviews      CourseReview[]

  @@map("courses")
}

model Module {
  id          String   @id @default(cuid())
  title       String
  description String?
  order       Int
  isPublished Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relationships
  courseId String
  course   Course @relation(fields: [courseId], references: [id], onDelete: Cascade)
  
  lessons  Lesson[]

  @@map("modules")
}

model Lesson {
  id          String      @id @default(cuid())
  title       String
  content     String // Rich text content
  type        LessonType
  videoUrl    String?
  duration    Int? // in minutes
  order       Int
  isPublished Boolean     @default(false)
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relationships
  moduleId  String
  module    Module @relation(fields: [moduleId], references: [id], onDelete: Cascade)
  
  creatorId String
  creator   User   @relation("LessonCreator", fields: [creatorId], references: [id])
  
  progress  LessonProgress[]
  quizzes   Quiz[]

  @@map("lessons")
}

model Enrollment {
  id           String           @id @default(cuid())
  progress     Float            @default(0) // 0-100
  status       EnrollmentStatus @default(ACTIVE)
  enrolledAt   DateTime         @default(now())
  completedAt  DateTime?
  lastAccessAt DateTime?

  // Relationships
  userId   String
  user     User   @relation(fields: [userId], references: [id])
  
  courseId String
  course   Course @relation(fields: [courseId], references: [id])

  @@unique([userId, courseId])
  @@map("enrollments")
}

model LessonProgress {
  id          String    @id @default(cuid())
  isCompleted Boolean   @default(false)
  timeSpent   Int       @default(0) // in seconds
  completedAt DateTime?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relationships
  userId   String
  user     User   @relation(fields: [userId], references: [id])
  
  lessonId String
  lesson   Lesson @relation(fields: [lessonId], references: [id])

  @@unique([userId, lessonId])
  @@map("lesson_progress")
}

model Quiz {
  id          String     @id @default(cuid())
  title       String
  description String?
  questions   Json // Array of question objects
  passingScore Int       @default(70)
  timeLimit   Int? // in minutes
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt

  // Relationships
  lessonId String
  lesson   Lesson @relation(fields: [lessonId], references: [id], onDelete: Cascade)
  
  attempts QuizAttempt[]

  @@map("quizzes")
}

model QuizAttempt {
  id        String   @id @default(cuid())
  answers   Json // User's answers
  score     Float
  passed    Boolean
  timeSpent Int // in seconds
  createdAt DateTime @default(now())

  // Relationships
  userId String
  user   User   @relation(fields: [userId], references: [id])

  quizId String
  quiz   Quiz   @relation(fields: [quizId], references: [id])

  @@map("quiz_attempts")
}

model Achievement {
  id          String   @id @default(cuid())
  name        String   @unique
  description String
  icon        String
  category    String
  points      Int      @default(0)
  criteria    Json // Criteria for earning this achievement
  createdAt   DateTime @default(now())

  // Relationships
  userAchievements UserAchievement[]

  @@map("achievements")
}

model UserAchievement {
  id        String   @id @default(cuid())
  earnedAt  DateTime @default(now())

  // Relationships
  userId        String
  user          User        @relation(fields: [userId], references: [id])
  
  achievementId String
  achievement   Achievement @relation(fields: [achievementId], references: [id])

  @@unique([userId, achievementId])
  @@map("user_achievements")
}

model Certificate {
  id          String   @id @default(cuid())
  title       String
  description String?
  imageUrl    String?
  issuedAt    DateTime @default(now())
  expiresAt   DateTime?

  // Relationships
  userId   String
  user     User   @relation(fields: [userId], references: [id])
  
  courseId String
  course   Course @relation(fields: [courseId], references: [id])

  @@map("certificates")
}

model ForumCategory {
  id          String   @id @default(cuid())
  name        String   @unique
  description String?
  color       String?
  order       Int      @default(0)
  createdAt   DateTime @default(now())

  // Relationships
  posts ForumPost[]

  @@map("forum_categories")
}

model ForumPost {
  id        String   @id @default(cuid())
  title     String
  content   String
  isPinned  Boolean  @default(false)
  isLocked  Boolean  @default(false)
  views     Int      @default(0)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relationships
  authorId String
  author   User   @relation(fields: [authorId], references: [id])
  
  categoryId String
  category   ForumCategory @relation(fields: [categoryId], references: [id])
  
  comments   ForumComment[]

  @@map("forum_posts")
}

model ForumComment {
  id        String   @id @default(cuid())
  content   String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relationships
  authorId String
  author   User      @relation(fields: [authorId], references: [id])
  
  postId   String
  post     ForumPost @relation(fields: [postId], references: [id], onDelete: Cascade)

  @@map("forum_comments")
}

model Mentorship {
  id        String           @id @default(cuid())
  status    MentorshipStatus @default(PENDING)
  message   String?
  createdAt DateTime         @default(now())
  updatedAt DateTime         @updatedAt

  // Relationships
  mentorId String
  mentor   User   @relation("MentorUser", fields: [mentorId], references: [id])
  
  menteeId String
  mentee   User   @relation("MenteeUser", fields: [menteeId], references: [id])

  @@unique([mentorId, menteeId])
  @@map("mentorships")
}

// Enums
enum UserRole {
  LEARNER
  INSTRUCTOR
  MENTOR
  ADMIN
  SUPER_ADMIN
}

enum CourseLevel {
  BEGINNER
  INTERMEDIATE
  ADVANCED
}

enum LessonType {
  TEXT
  VIDEO
  INTERACTIVE
  QUIZ
  ASSIGNMENT
}

enum EnrollmentStatus {
  ACTIVE
  COMPLETED
  PAUSED
  CANCELLED
}

enum MentorshipStatus {
  PENDING
  ACTIVE
  COMPLETED
  CANCELLED
}
