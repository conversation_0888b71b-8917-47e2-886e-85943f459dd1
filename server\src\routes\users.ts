import { Router } from 'express';
import { authenticate, authorize, authorize<PERSON><PERSON>er<PERSON>rAd<PERSON> } from '../middleware/auth.js';
import { validate, schemas } from '../middleware/validation.js';
import { asyncHandler } from '../middleware/errorHandler.js';

const router = Router();

/**
 * @swagger
 * /api/users/profile:
 *   put:
 *     summary: Update user profile
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               firstName:
 *                 type: string
 *               lastName:
 *                 type: string
 *               bio:
 *                 type: string
 *               country:
 *                 type: string
 *               timezone:
 *                 type: string
 *               preferredLanguage:
 *                 type: string
 *     responses:
 *       200:
 *         description: Profile updated successfully
 */
router.put('/profile',
  authenticate,
  validate({ body: schemas.updateProfile }),
  asyncHandler(async (req, res) => {
    res.json({
      success: true,
      message: 'User profile routes - Coming soon!',
      data: { endpoint: 'PUT /api/users/profile' }
    });
  })
);

/**
 * @swagger
 * /api/users/change-password:
 *   put:
 *     summary: Change user password
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Password changed successfully
 */
router.put('/change-password',
  authenticate,
  validate({ body: schemas.changePassword }),
  asyncHandler(async (req, res) => {
    res.json({
      success: true,
      message: 'Change password - Coming soon!',
      data: { endpoint: 'PUT /api/users/change-password' }
    });
  })
);

/**
 * @swagger
 * /api/users/{id}:
 *   get:
 *     summary: Get user by ID
 *     tags: [Users]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: User retrieved successfully
 */
router.get('/:id',
  validate({ params: schemas.idParam }),
  asyncHandler(async (req, res) => {
    res.json({
      success: true,
      message: 'Get user by ID - Coming soon!',
      data: { endpoint: `GET /api/users/${req.params.id}` }
    });
  })
);

export default router;
